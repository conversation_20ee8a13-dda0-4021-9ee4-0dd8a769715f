# 🔍 DIAGNÓSTICO DE HERRAMIENTAS - theHarvester y Nuclei

## 🎯 **PROBLEMA IDENTIFICADO**

**theHarvester y Nuclei no funcionan porque probablemente NO ESTÁN INSTALADOS en Railway.**

## 🔧 **SISTEMA DE DIAGNÓSTICO IMPLEMENTADO**

### **1. Verificación Automática al Inicio:**
```
🔍 DIAGNÓSTICO DEL SISTEMA
============================================================
✅ whois: Encontrado en /usr/bin/whois
   Versión: whois 5.5.7
✅ dig: Encontrado en /usr/bin/dig
   Versión: DiG 9.18.1
❌ theharvester: No encontrado
❌ nuclei: No encontrado
✅ assetfinder: Encontrado en /usr/local/bin/assetfinder
   Versión: assetfinder v0.1.1
============================================================
📊 RESUMEN: 3/5 herramientas disponibles
✅ Disponibles: whois, dig, assetfinder
❌ Faltantes: theharvester, nuclei
============================================================
```

### **2. Endpoint de Diagnóstico Web:**
**URL:** `https://securityscannerweb-production.up.railway.app/diagnostic`

**Respuesta JSON:**
```json
{
  "timestamp": "2024-01-15T10:30:00",
  "summary": {
    "available_tools": 3,
    "total_tools": 5,
    "percentage": 60.0
  },
  "tools": {
    "whois": {
      "available": true,
      "path": "/usr/bin/whois",
      "version": "whois 5.5.7",
      "status": "OK"
    },
    "dig": {
      "available": true,
      "path": "/usr/bin/dig", 
      "version": "DiG 9.18.1",
      "status": "OK"
    },
    "theharvester": {
      "available": false,
      "path": null,
      "version": null,
      "status": "NOT_FOUND"
    },
    "nuclei": {
      "available": false,
      "path": null,
      "version": null,
      "status": "NOT_FOUND"
    },
    "assetfinder": {
      "available": true,
      "path": "/usr/local/bin/assetfinder",
      "version": "assetfinder v0.1.1",
      "status": "OK"
    }
  },
  "environment": {
    "port": "auto",
    "railway_env": "production",
    "python_version": "3.11.0"
  }
}
```

### **3. Manejo Inteligente de Herramientas Faltantes:**

#### **theHarvester:**
```python
# Si no está instalado:
{
  "status": "error",
  "progress": "theHarvester no está instalado",
  "errors": "theHarvester no encontrado en el sistema",
  "output": "Herramienta no disponible en este entorno"
}
```

#### **Nuclei:**
```python
# Si no está instalado:
{
  "status": "error", 
  "progress": "Nuclei no está instalado",
  "errors": "Nuclei no encontrado en el sistema",
  "output": "Herramienta no disponible en este entorno"
}
```

## 🚀 **CÓMO VERIFICAR EL PROBLEMA**

### **1. Accede al diagnóstico:**
`https://securityscannerweb-production.up.railway.app/diagnostic`

### **2. Revisa los logs de Railway:**
```
🔍 DIAGNÓSTICO DEL SISTEMA
============================================================
❌ theharvester: No encontrado
❌ nuclei: No encontrado
============================================================
🔧 CARACTERÍSTICAS HABILITADAS:
✅ Service Worker funcionando
✅ Persistencia de scans habilitada
✅ Manejo robusto de errores
✅ Diagnóstico automático de herramientas
❌ theHarvester NO disponible
❌ Nuclei NO disponible
============================================================
```

### **3. Haz un scan de prueba:**
- Las herramientas faltantes mostrarán estado "error"
- Con mensaje claro: "Herramienta no disponible en este entorno"

## 🔧 **SOLUCIONES POSIBLES**

### **Opción 1: Instalar en Dockerfile**
```dockerfile
# Agregar al Dockerfile:
RUN apt-get update && apt-get install -y \
    python3-pip \
    git

# Instalar theHarvester
RUN pip3 install theHarvester

# Instalar Nuclei
RUN wget https://github.com/projectdiscovery/nuclei/releases/download/v3.1.0/nuclei_3.1.0_linux_amd64.zip \
    && unzip nuclei_3.1.0_linux_amd64.zip \
    && mv nuclei /usr/local/bin/ \
    && chmod +x /usr/local/bin/nuclei
```

### **Opción 2: Usar Nixpacks (Railway)**
```toml
# nixpacks.toml
[phases.setup]
nixPkgs = ["python3", "pip", "git", "wget", "unzip"]

[phases.install]
cmds = [
    "pip install theHarvester",
    "wget https://github.com/projectdiscovery/nuclei/releases/download/v3.1.0/nuclei_3.1.0_linux_amd64.zip",
    "unzip nuclei_3.1.0_linux_amd64.zip",
    "mv nuclei /usr/local/bin/",
    "chmod +x /usr/local/bin/nuclei"
]
```

### **Opción 3: Alternativas Ligeras**
```python
# Implementar versiones simplificadas usando APIs:
def run_theharvester_api(self):
    """Usar APIs públicas en lugar de theHarvester"""
    # Implementar búsqueda de emails usando APIs web
    
def run_nuclei_lite(self):
    """Usar verificaciones básicas en lugar de Nuclei"""
    # Implementar checks básicos de seguridad
```

## 📊 **ESTADO ACTUAL**

### **✅ Funcionando:**
- whois - Información de dominios
- dig - Registros DNS  
- assetfinder - Subdominios (si está instalado)

### **❌ No Funcionando:**
- theHarvester - Recolección OSINT
- Nuclei - Scanner de vulnerabilidades

### **🔧 Diagnóstico:**
- ✅ Sistema de verificación automática
- ✅ Logs detallados
- ✅ Endpoint web de diagnóstico
- ✅ Manejo graceful de herramientas faltantes

## 🎯 **PRÓXIMOS PASOS**

1. **Verificar diagnóstico** en Railway
2. **Confirmar herramientas faltantes**
3. **Decidir solución:**
   - Instalar herramientas reales
   - Implementar alternativas ligeras
   - Usar APIs externas

---

## 🔍 **¡DIAGNÓSTICO COMPLETO IMPLEMENTADO!**

**Ahora sabemos exactamente qué herramientas están disponibles y cuáles faltan en Railway.**

**Accede a `/diagnostic` para ver el estado completo del sistema.**
