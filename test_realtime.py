#!/usr/bin/env python3
"""
Script para probar la funcionalidad de tiempo real del Security Scanner
"""

import requests
import json
import time

BASE_URL = "https://securityscannerweb-production.up.railway.app"

def test_realtime_functionality():
    """Probar la funcionalidad de tiempo real"""
    print("🧪 Probando funcionalidad de tiempo real...")
    
    # 1. Verificar estado del sistema
    print("\n📊 Verificando estado del sistema...")
    try:
        response = requests.get(f"{BASE_URL}/debug/scans")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Scans activos: {data['active_scans']['count']}")
            print(f"💾 Scans guardados: {data['saved_scans']['count']}")
            print(f"🚂 Entorno: {data['system_info']['railway_env']}")
            
            # Si hay scans activos, probar el endpoint de tiempo real
            if data['active_scans']['count'] > 0:
                for scan_id, scan_info in data['active_scans']['scans'].items():
                    print(f"\n🔍 Probando tiempo real para scan: {scan_id[:8]}...")
                    print(f"   Target: {scan_info['target']}")
                    print(f"   Estado: {scan_info['status']}")
                    
                    # Probar endpoint de tiempo real
                    realtime_response = requests.get(f"{BASE_URL}/realtime/{scan_id}")
                    if realtime_response.status_code == 200:
                        realtime_data = realtime_response.json()
                        print("✅ Endpoint de tiempo real funcionando!")
                        
                        # Mostrar información de herramientas
                        tools = realtime_data.get('tools', {})
                        print(f"🔧 Herramientas encontradas: {len(tools)}")
                        
                        for tool_name, tool_data in tools.items():
                            status = tool_data.get('status', 'unknown')
                            lines_count = tool_data.get('total_lines', 0)
                            progress = tool_data.get('progress', 'N/A')
                            
                            status_emoji = {
                                'completed': '✅',
                                'running': '🔄',
                                'error': '❌',
                                'timeout': '⏰'
                            }.get(status, '❓')
                            
                            print(f"   {status_emoji} {tool_name}: {status} ({lines_count} líneas)")
                            if progress != 'N/A':
                                print(f"      Progreso: {progress}")
                            
                            # Mostrar algunas líneas de ejemplo
                            lines = tool_data.get('lines', [])
                            if lines:
                                print(f"      Últimas líneas:")
                                for line_data in lines[-3:]:  # Últimas 3 líneas
                                    timestamp = line_data.get('timestamp', '')
                                    line = line_data.get('line', '')
                                    print(f"        [{timestamp[-8:]}] {line[:50]}...")
                    else:
                        print(f"❌ Error en endpoint de tiempo real: {realtime_response.status_code}")
            else:
                print("ℹ️ No hay scans activos para probar tiempo real")
                
        else:
            print(f"❌ Error obteniendo estado del sistema: {response.status_code}")
            
    except Exception as e:
        print(f"💥 Error: {e}")

def test_scan_initiation():
    """Intentar iniciar un nuevo scan para probar tiempo real"""
    print("\n🚀 Intentando iniciar nuevo scan...")
    
    try:
        response = requests.post(
            f"{BASE_URL}/scan",
            json={"target": "httpbin.org"},  # Usar un target diferente
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            scan_id = data.get('scan_id')
            print(f"✅ Scan iniciado exitosamente!")
            print(f"🆔 Scan ID: {scan_id}")
            
            # Monitorear tiempo real por unos segundos
            print("📡 Monitoreando tiempo real...")
            for i in range(5):  # 5 intentos
                time.sleep(3)
                
                realtime_response = requests.get(f"{BASE_URL}/realtime/{scan_id}")
                if realtime_response.status_code == 200:
                    realtime_data = realtime_response.json()
                    status = realtime_data.get('status', 'unknown')
                    tools = realtime_data.get('tools', {})
                    
                    print(f"   [{i+1}/5] Estado: {status}, Herramientas: {len(tools)}")
                    
                    # Mostrar herramientas activas
                    for tool_name, tool_data in tools.items():
                        tool_status = tool_data.get('status', 'unknown')
                        if tool_status == 'running':
                            progress = tool_data.get('progress', 'N/A')
                            print(f"      🔄 {tool_name}: {progress}")
                    
                    if status in ['completed', 'completed_with_errors', 'error']:
                        print("🎉 Scan completado!")
                        break
                else:
                    print(f"   ⚠️ Error obteniendo tiempo real: {realtime_response.status_code}")
                    
        elif response.status_code == 429:
            print("⚠️ Límite de scans concurrentes alcanzado (esperado)")
            print("   Esto significa que el sistema está funcionando correctamente")
        else:
            print(f"❌ Error iniciando scan: {response.status_code}")
            print(f"   Respuesta: {response.text}")
            
    except Exception as e:
        print(f"💥 Error: {e}")

if __name__ == "__main__":
    print("🔒 Security Scanner - Prueba de Funcionalidad de Tiempo Real")
    print("=" * 70)
    
    # Probar funcionalidad existente
    test_realtime_functionality()
    
    # Intentar nuevo scan
    test_scan_initiation()
    
    print("\n" + "=" * 70)
    print("✅ Prueba completada!")
