#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Security Scanner Web Interface
Herramienta web interactiva para análisis de seguridad con múltiples herramientas
"""

import os
import json
import uuid
import subprocess
import threading
import sys
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file
from werkzeug.utils import secure_filename
import time
import re

app = Flask(__name__)
app.secret_key = 'security_scanner_2024'

# Configuración optimizada para Railway
REPORTS_DIR = 'reports'
MAX_CONCURRENT_SCANS = 2  # Reducido para Railway
active_scans = {}
scan_results = {}

# Configuración de recursos para Railway
import os
RAILWAY_ENV = os.environ.get('RAILWAY_ENVIRONMENT_NAME', 'local')
IS_RAILWAY = RAILWAY_ENV != 'local'

if IS_RAILWAY:
    print("🚂 Ejecutando en Railway - Configuración optimizada para recursos limitados")
    MAX_CONCURRENT_SCANS = 1  # Solo 1 scan simultáneo en Railway
else:
    print("🏠 Ejecutando en entorno local")

# Asegurar que el directorio de reportes existe
os.makedirs(REPORTS_DIR, exist_ok=True)

def save_scan_results(scan_id, results):
    """Guarda los resultados del scan en un archivo JSON"""
    try:
        results_file = os.path.join(REPORTS_DIR, f"scan_{scan_id}.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"Resultados guardados: {results_file}")
    except Exception as e:
        print(f"Error guardando resultados: {e}")

def load_scan_results(scan_id):
    """Carga los resultados del scan desde un archivo JSON"""
    try:
        results_file = os.path.join(REPORTS_DIR, f"scan_{scan_id}.json")
        if os.path.exists(results_file):
            with open(results_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    except Exception as e:
        print(f"Error cargando resultados: {e}")
        return None

def cleanup_old_scans():
    """Limpia scans antiguos para liberar espacio"""
    try:
        import glob
        import time

        # Buscar archivos de scan más antiguos de 24 horas
        scan_files = glob.glob(os.path.join(REPORTS_DIR, "scan_*.json"))
        current_time = time.time()

        for file_path in scan_files:
            file_age = current_time - os.path.getmtime(file_path)
            # Si el archivo tiene más de 24 horas (86400 segundos)
            if file_age > 86400:
                try:
                    os.remove(file_path)
                    print(f"Archivo de scan antiguo eliminado: {file_path}")
                except:
                    pass

    except Exception as e:
        print(f"Error limpiando scans antiguos: {e}")

def diagnose_system():
    """Diagnostica qué herramientas están disponibles en el sistema"""
    print("=" * 60)
    print("🔍 DIAGNÓSTICO DEL SISTEMA")
    print("=" * 60)

    tools_to_check = [
        ('whois', 'whois --version'),
        ('dig', 'dig -v'),
        ('theharvester', 'theharvester --version'),
        ('assetfinder', 'assetfinder --help'),
        ('nuclei', 'nuclei -version')
    ]

    available_tools = []

    for tool_name, check_cmd in tools_to_check:
        try:
            # Verificar si está en PATH
            which_result = subprocess.run(f"which {tool_name}", shell=True, capture_output=True, text=True, timeout=5)

            if which_result.returncode == 0:
                print(f"✅ {tool_name}: Encontrado en {which_result.stdout.strip()}")

                # Verificar versión
                try:
                    version_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if version_result.returncode == 0:
                        version_info = version_result.stdout.strip()[:100]
                        print(f"   Versión: {version_info}")
                    else:
                        version_info = version_result.stderr.strip()[:100]
                        print(f"   Info: {version_info}")
                except:
                    print(f"   Versión: No disponible")

                available_tools.append(tool_name)
            else:
                print(f"❌ {tool_name}: No encontrado")

        except Exception as e:
            print(f"❌ {tool_name}: Error verificando - {str(e)[:50]}")

    print("=" * 60)
    print(f"📊 RESUMEN: {len(available_tools)}/{len(tools_to_check)} herramientas disponibles")
    print(f"✅ Disponibles: {', '.join(available_tools) if available_tools else 'Ninguna'}")

    missing_tools = [tool for tool, _ in tools_to_check if tool not in available_tools]
    if missing_tools:
        print(f"❌ Faltantes: {', '.join(missing_tools)}")

    print("=" * 60)

    return available_tools

class SecurityScanner:
    def __init__(self, target, scan_id):
        self.target = target
        self.scan_id = scan_id
        self.results = {
            'target': target,
            'scan_id': scan_id,
            'start_time': datetime.now().isoformat(),
            'end_time': None,
            'status': 'initializing',
            'progress': 'Inicializando análisis...',
            'tools_results': {},
            'vulnerabilities': [],
            'recommendations': [],
            'summary': {
                'total_tools': 5,
                'completed_tools': 0,
                'failed_tools': 0,
                'vulnerabilities_found': 0
            }
        }

        # Inicializar todas las herramientas con estado inicial
        tools = ['whois', 'dig', 'theharvester', 'assetfinder', 'nuclei']
        for tool in tools:
            self.results['tools_results'][tool] = {
                'status': 'pending',
                'progress': 'Esperando...',
                'output': '',
                'errors': ''
            }

        print(f"Scanner inicializado para {target} con ID {scan_id}")

    def run_command(self, command, tool_name, timeout=30):
        """Ejecuta un comando con captura en tiempo real"""
        try:
            self.results['tools_results'][tool_name] = {
                'status': 'running',
                'output': '',
                'errors': '',
                'progress': 'Iniciando...'
            }

            print(f"[{tool_name}] Ejecutando: {command}")

            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            output_lines = []
            error_lines = []

            try:
                stdout, stderr = process.communicate(timeout=timeout)

                if stdout:
                    output_lines.append(stdout.strip())
                if stderr:
                    error_lines.append(stderr.strip())

                final_output = '\n'.join(filter(None, output_lines))
                final_errors = '\n'.join(filter(None, error_lines))

                # Determinar estado final
                if process.returncode == 0:
                    status = 'completed'
                elif process.returncode == 124:  # timeout command
                    status = 'timeout'
                else:
                    status = 'error'

                self.results['tools_results'][tool_name] = {
                    'status': status,
                    'output': final_output,
                    'errors': final_errors,
                    'return_code': process.returncode,
                    'progress': f'Completado - {status}'
                }

                print(f"[{tool_name}] Terminado con estado: {status}")
                return final_output, final_errors, process.returncode

            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
                self.results['tools_results'][tool_name].update({
                    'status': 'timeout',
                    'errors': f'Timeout después de {timeout} segundos',
                    'progress': 'Timeout'
                })
                print(f"[{tool_name}] Timeout después de {timeout} segundos")
                return '', f'Timeout después de {timeout} segundos', 1

        except Exception as e:
            self.results['tools_results'][tool_name].update({
                'status': 'error',
                'errors': str(e),
                'progress': f'Error: {str(e)}'
            })
            print(f"[{tool_name}] Error: {str(e)}")
            return '', str(e), 1

    def run_whois(self):
        """Ejecuta análisis WHOIS (optimizado)"""
        command = f"timeout 15 whois {self.target}"
        stdout, stderr, returncode = self.run_command(command, 'whois', timeout=20)

        if returncode == 0:
            self.parse_whois_output(stdout)

    def run_dig(self):
        """Ejecuta análisis DNS con dig (optimizado para velocidad)"""
        try:
            # Solo los registros más importantes con timeouts cortos
            commands = {
                'A': f"timeout 10 dig A {self.target} +short +time=2 +tries=1",
                'MX': f"timeout 10 dig MX {self.target} +short +time=2 +tries=1",
                'NS': f"timeout 10 dig NS {self.target} +short +time=2 +tries=1"
            }

            dig_results = {}
            all_successful = True

            for record_type, command in commands.items():
                stdout, stderr, returncode = self.run_command(command, f'dig_{record_type}', timeout=15)
                dig_results[record_type] = stdout.strip().split('\n') if stdout.strip() else []
                if returncode != 0:
                    all_successful = False

            # Set the main dig tool status correctly
            self.results['tools_results']['dig'] = {
                'status': 'completed' if all_successful else 'completed_with_errors',
                'output': 'DNS queries completed',
                'errors': '' if all_successful else 'Some DNS queries failed'
            }

            self.results['tools_results']['dig_summary'] = {
                'status': 'completed' if all_successful else 'completed_with_errors',
                'output': dig_results
            }

        except Exception as e:
            self.results['tools_results']['dig'] = {
                'status': 'error',
                'errors': str(e),
                'output': ''
            }
            self.results['tools_results']['dig_summary'] = {
                'status': 'error',
                'errors': str(e),
                'output': {}
            }

    def run_theharvester(self):
        """Ejecuta theHarvester con verificación de instalación"""
        try:
            # Primero verificar si theHarvester está instalado
            check_cmd = "which theharvester"
            check_stdout, check_stderr, check_returncode = self.run_command(check_cmd, 'theharvester_check', timeout=5)

            if check_returncode != 0:
                print(f"[theharvester] No está instalado o no está en PATH")
                self.results['tools_results']['theharvester'] = {
                    'status': 'error',
                    'progress': 'theHarvester no está instalado',
                    'errors': 'theHarvester no encontrado en el sistema',
                    'output': 'Herramienta no disponible en este entorno'
                }
                return

            print(f"[theharvester] Encontrado en: {check_stdout.strip()}")

            # Verificar versión
            version_cmd = "theharvester --version"
            version_stdout, version_stderr, version_returncode = self.run_command(version_cmd, 'theharvester_version', timeout=10)

            if version_returncode == 0:
                print(f"[theharvester] Versión: {version_stdout.strip()}")

            # Usar solo fuentes confiables que funcionan
            sources = "bing,duckduckgo"  # Reducir a solo las más confiables

            # Comando simplificado
            command = f"timeout 30 theharvester -d {self.target} -l 50 -b {sources}"
            print(f"[theharvester] Ejecutando: {command}")

            stdout, stderr, returncode = self.run_command(command, 'theharvester', timeout=35)

            print(f"[theharvester] Return code: {returncode}")
            print(f"[theharvester] STDOUT length: {len(stdout) if stdout else 0}")
            print(f"[theharvester] STDERR: {stderr[:200] if stderr else 'None'}")

            # Procesar output independientemente del return code
            if stdout and len(stdout.strip()) > 0:
                print(f"[theharvester] Output recibido: {len(stdout)} caracteres")
                self.parse_theharvester_output(stdout)

                # Marcar como completado si hay output
                if 'theharvester' in self.results['tools_results']:
                    self.results['tools_results']['theharvester']['status'] = 'completed'
                    self.results['tools_results']['theharvester']['progress'] = 'Análisis completado'
            else:
                print(f"[theharvester] Sin output útil")
                if 'theharvester' in self.results['tools_results']:
                    self.results['tools_results']['theharvester']['status'] = 'completed'
                    self.results['tools_results']['theharvester']['progress'] = 'Sin resultados encontrados'
                    self.results['tools_results']['theharvester']['output'] = 'No se encontraron emails o subdominios'
                    if stderr:
                        self.results['tools_results']['theharvester']['errors'] = stderr[:500]

        except Exception as e:
            print(f"[theharvester] Error crítico: {e}")
            if 'theharvester' in self.results['tools_results']:
                self.results['tools_results']['theharvester']['status'] = 'error'
                self.results['tools_results']['theharvester']['errors'] = str(e)

    def run_assetfinder(self):
        """Ejecuta Assetfinder para enumeración ultrarrápida de subdominios"""
        # Timeout corto para máxima velocidad
        command = f"timeout 25 assetfinder --subs-only {self.target} | head -50"
        self.run_command(command, 'assetfinder', timeout=30)

    def run_nuclei(self):
        """Ejecuta Nuclei con verificación de instalación y diagnóstico"""
        try:
            # Primero verificar si Nuclei está instalado
            check_cmd = "which nuclei"
            check_stdout, check_stderr, check_returncode = self.run_command(check_cmd, 'nuclei_check', timeout=5)

            if check_returncode != 0:
                print(f"[nuclei] No está instalado o no está en PATH")
                self.results['tools_results']['nuclei'] = {
                    'status': 'error',
                    'progress': 'Nuclei no está instalado',
                    'errors': 'Nuclei no encontrado en el sistema',
                    'output': 'Herramienta no disponible en este entorno'
                }
                return

            print(f"[nuclei] Encontrado en: {check_stdout.strip()}")

            # Verificar versión
            version_cmd = "nuclei -version"
            version_stdout, version_stderr, version_returncode = self.run_command(version_cmd, 'nuclei_version', timeout=10)

            if version_returncode == 0:
                print(f"[nuclei] Versión: {version_stdout.strip()}")

            # Verificar templates
            templates_cmd = "nuclei -tl | head -5"
            templates_stdout, templates_stderr, templates_returncode = self.run_command(templates_cmd, 'nuclei_templates', timeout=15)

            if templates_returncode == 0:
                print(f"[nuclei] Templates disponibles: {len(templates_stdout.split()) if templates_stdout else 0}")
            else:
                print(f"[nuclei] Problema con templates: {templates_stderr[:200] if templates_stderr else 'Unknown'}")

            # Configuración ultra-ligera para Railway
            target_with_protocol = self.target if self.target.startswith(('http://', 'https://')) else f"https://{self.target}"

            # Comando ultra-simplificado para diagnóstico
            command = (
                f"timeout 30 nuclei -target {target_with_protocol} "
                f"-jsonl -silent -no-color "
                f"-severity critical,high "
                f"-timeout 5 -retries 1 -c 1 -rl 5 "
                f"-tags cve "
                f"-exclude-tags dos,intrusive,bruteforce,fuzz "
                f"-nh -duc -ni -disable-update-check "
                f"-max-host-error 2"
            )

            print(f"[nuclei] Ejecutando análisis ultra-ligero para: {target_with_protocol}")
            print(f"[nuclei] Comando: {command}")

            stdout, stderr, returncode = self.run_command(command, 'nuclei', timeout=35)

            print(f"[nuclei] Return code: {returncode}")
            print(f"[nuclei] STDOUT length: {len(stdout) if stdout else 0}")
            print(f"[nuclei] STDERR: {stderr[:300] if stderr else 'None'}")

            # Procesar resultados con manejo mejorado de errores
            if stdout and stdout.strip():
                # Filtrar líneas JSON válidas
                valid_lines = []
                for line in stdout.strip().split('\n'):
                    line = line.strip()
                    if line and line.startswith('{') and line.endswith('}'):
                        try:
                            # Verificar que es JSON válido
                            json.loads(line)
                            valid_lines.append(line)
                        except:
                            continue

                if valid_lines:
                    print(f"[nuclei] Procesando {len(valid_lines)} vulnerabilidades encontradas")
                    self.parse_nuclei_output('\n'.join(valid_lines))
                    self.results['tools_results']['nuclei']['status'] = 'completed'
                    self.results['tools_results']['nuclei']['progress'] = f'{len(valid_lines)} vulnerabilidades encontradas'
                else:
                    print(f"[nuclei] Análisis completado - sin vulnerabilidades")
                    self.results['tools_results']['nuclei']['status'] = 'completed'
                    self.results['tools_results']['nuclei']['progress'] = 'Sin vulnerabilidades detectadas'
                    self.results['tools_results']['nuclei']['output'] = 'Análisis completado sin vulnerabilidades'

            elif returncode == 124:
                print(f"[nuclei] Timeout - análisis parcial completado")
                self.results['tools_results']['nuclei']['status'] = 'timeout'
                self.results['tools_results']['nuclei']['progress'] = 'Timeout - análisis parcial'
                self.results['tools_results']['nuclei']['output'] = 'Análisis parcial por timeout'

            elif "failed to create new OS thread" in str(stderr):
                print(f"[nuclei] Error de recursos - limitaciones de Railway")
                self.results['tools_results']['nuclei']['status'] = 'error'
                self.results['tools_results']['nuclei']['progress'] = 'Error de recursos del sistema'
                self.results['tools_results']['nuclei']['errors'] = 'Limitaciones de recursos en Railway'
                self.results['tools_results']['nuclei']['output'] = 'Nuclei requiere más recursos de los disponibles'

            elif "permission denied" in str(stderr).lower():
                print(f"[nuclei] Error de permisos")
                self.results['tools_results']['nuclei']['status'] = 'error'
                self.results['tools_results']['nuclei']['progress'] = 'Error de permisos'
                self.results['tools_results']['nuclei']['errors'] = 'Permisos insuficientes para ejecutar Nuclei'
                self.results['tools_results']['nuclei']['output'] = 'Error de permisos en el sistema'

            else:
                print(f"[nuclei] Análisis completado sin resultados")
                self.results['tools_results']['nuclei']['status'] = 'completed'
                self.results['tools_results']['nuclei']['progress'] = 'Sin vulnerabilidades detectadas'
                self.results['tools_results']['nuclei']['output'] = 'Análisis completado - objetivo seguro'
                if stderr:
                    self.results['tools_results']['nuclei']['errors'] = stderr[:500]

        except Exception as e:
            print(f"[nuclei] Error crítico: {str(e)}")
            self.results['tools_results']['nuclei'] = {
                'status': 'error',
                'errors': f'Error crítico: {str(e)}',
                'output': 'Error durante la ejecución de Nuclei',
                'progress': f'Error: {str(e)[:100]}'
            }

    def parse_whois_output(self, output):
        """Parsea la salida de WHOIS y extrae información relevante"""
        whois_info = {}
        for line in output.split('\n'):
            if ':' in line:
                key, value = line.split(':', 1)
                whois_info[key.strip()] = value.strip()

        # Verificar fechas de expiración
        for key in whois_info:
            if 'expir' in key.lower() or 'renew' in key.lower():
                self.check_domain_expiration(whois_info[key])

    def parse_theharvester_output(self, output):
        """Parsea la salida de theHarvester y extrae emails"""
        emails = set()
        hosts = set()

        # Buscar emails con regex
        email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        found_emails = re.findall(email_pattern, output)
        emails.update(found_emails)

        # Buscar hosts/subdominios
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            # Si la línea contiene el dominio objetivo y parece un subdominio
            if self.target in line and ('.' in line) and not line.startswith('*'):
                # Limpiar la línea
                cleaned = re.sub(r'^[\s*-]+', '', line)
                if cleaned and not cleaned.startswith('[') and not cleaned.startswith('*'):
                    hosts.add(cleaned)

        # Actualizar resultados
        if 'theharvester' in self.results['tools_results']:
            self.results['tools_results']['theharvester']['emails_found'] = list(emails)
            self.results['tools_results']['theharvester']['hosts_found'] = list(hosts)
            print(f"[theharvester] Encontrados: {len(emails)} emails, {len(hosts)} hosts")

    def parse_nuclei_output(self, output):
        """Parsea la salida JSON de Nuclei con manejo mejorado de errores"""
        vulnerabilities = []
        processed_lines = 0
        error_lines = 0

        for line in output.strip().split('\n'):
            if line.strip():
                processed_lines += 1
                try:
                    vuln = json.loads(line)

                    # Extraer información con valores por defecto seguros
                    info = vuln.get('info', {})
                    template_id = vuln.get('template-id', 'unknown')
                    name = info.get('name', template_id)
                    severity = info.get('severity', 'info').lower()
                    description = info.get('description', 'No description available')
                    matched_at = vuln.get('matched-at', vuln.get('host', ''))
                    vuln_type = vuln.get('type', 'unknown')
                    host = vuln.get('host', self.target)
                    timestamp = vuln.get('timestamp', '')
                    tags = info.get('tags', [])

                    # Información adicional
                    reference = info.get('reference', [])
                    classification = info.get('classification', {})

                    vulnerability = {
                        'template_id': template_id,
                        'name': name,
                        'severity': severity,
                        'description': description,
                        'matched_at': matched_at,
                        'type': vuln_type,
                        'host': host,
                        'timestamp': timestamp,
                        'tags': tags if isinstance(tags, list) else [],
                        'reference': reference if isinstance(reference, list) else [],
                        'classification': classification if isinstance(classification, dict) else {},
                        'curl_command': vuln.get('curl-command', ''),
                        'extracted_results': vuln.get('extracted-results', [])
                    }

                    vulnerabilities.append(vulnerability)

                except json.JSONDecodeError as e:
                    error_lines += 1
                    print(f"[nuclei] Error parsing JSON line {processed_lines}: {str(e)}")
                    continue
                except Exception as e:
                    error_lines += 1
                    print(f"[nuclei] Error processing line {processed_lines}: {str(e)}")
                    continue

        # Ordenar vulnerabilidades por severidad
        severity_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3, 'info': 4}
        vulnerabilities.sort(key=lambda x: severity_order.get(x['severity'], 5))

        # Actualizar resultados
        self.results['vulnerabilities'].extend(vulnerabilities)

        # Actualizar estadísticas de nuclei
        if 'nuclei' in self.results['tools_results']:
            self.results['tools_results']['nuclei'].update({
                'vulnerabilities_count': len(vulnerabilities),
                'processed_lines': processed_lines,
                'error_lines': error_lines,
                'progress': f'Completado - {len(vulnerabilities)} vulnerabilidades encontradas'
            })

        print(f"[nuclei] Procesadas {processed_lines} líneas, {len(vulnerabilities)} vulnerabilidades válidas, {error_lines} errores")

    def check_domain_expiration(self, date_str):
        """Verifica si el dominio está próximo a expirar"""
        # Implementación básica - se puede mejorar
        if 'month' in date_str.lower() or '30' in date_str:
            self.results['recommendations'].append({
                'type': 'warning',
                'message': 'El dominio podría estar próximo a expirar. Verificar fecha de renovación.'
            })

    def generate_recommendations(self):
        """Genera recomendaciones basadas en los resultados"""
        recommendations = []

        # Recomendaciones basadas en vulnerabilidades
        if self.results['vulnerabilities']:
            high_severity = [v for v in self.results['vulnerabilities'] if v['severity'] == 'high']
            if high_severity:
                recommendations.append({
                    'type': 'critical',
                    'message': f'Se encontraron {len(high_severity)} vulnerabilidades de alta severidad. Revisar inmediatamente.'
                })

        # Recomendaciones generales
        recommendations.extend([
            {
                'type': 'info',
                'message': 'Implementar un WAF (Web Application Firewall) si no está presente.'
            },
            {
                'type': 'info',
                'message': 'Mantener todas las aplicaciones y dependencias actualizadas.'
            },
            {
                'type': 'info',
                'message': 'Realizar análisis de seguridad periódicos.'
            }
        ])

        self.results['recommendations'].extend(recommendations)

    def run_full_scan(self):
        """Ejecuta el análisis completo y espera a que todas las herramientas terminen"""
        try:
            print(f"Iniciando análisis completo para: {self.target}")
            self.results['status'] = 'running'
            self.results['progress'] = 'Análisis en progreso...'

            # Lista de herramientas en orden de ejecución
            tools_to_run = [
                ('whois', self.run_whois),
                ('dig', self.run_dig),
                ('theharvester', self.run_theharvester),
                ('assetfinder', self.run_assetfinder),
                ('nuclei', self.run_nuclei)
            ]

            completed_count = 0
            total_tools = len(tools_to_run)

            # Ejecutar cada herramienta y verificar su estado
            for i, (tool_name, tool_function) in enumerate(tools_to_run, 1):
                print(f"Ejecutando {tool_name} ({i}/{total_tools})...")

                # Actualizar progreso general
                self.results['progress'] = f'Ejecutando {tool_name} ({i}/{total_tools})'

                try:
                    # Marcar como iniciada
                    self.results['tools_results'][tool_name]['status'] = 'running'
                    self.results['tools_results'][tool_name]['progress'] = 'Ejecutando...'

                    # Ejecutar la herramienta
                    tool_function()

                    # Verificar que terminó correctamente
                    tool_result = self.results['tools_results'].get(tool_name, {})
                    if tool_result.get('status') not in ['completed', 'error', 'timeout']:
                        # Si no terminó, marcar como completado por defecto
                        self.results['tools_results'][tool_name]['status'] = 'completed'
                        self.results['tools_results'][tool_name]['progress'] = 'Completado'
                        if not tool_result.get('output'):
                            self.results['tools_results'][tool_name]['output'] = 'Análisis completado'

                    # Actualizar contador
                    if tool_result.get('status') == 'completed':
                        completed_count += 1
                        self.results['summary']['completed_tools'] = completed_count
                    elif tool_result.get('status') in ['error', 'timeout']:
                        self.results['summary']['failed_tools'] += 1

                    print(f"{tool_name} completado con estado: {tool_result.get('status')}")

                except Exception as e:
                    print(f"Error ejecutando {tool_name}: {str(e)}")
                    self.results['tools_results'][tool_name] = {
                        'status': 'error',
                        'errors': str(e),
                        'output': f'Error durante la ejecución: {str(e)}',
                        'progress': f'Error: {str(e)[:50]}'
                    }
                    self.results['summary']['failed_tools'] += 1

            # Verificar que todas las herramientas han terminado
            all_completed = True
            for tool_name, _ in tools_to_run:
                tool_status = self.results['tools_results'].get(tool_name, {}).get('status')
                if tool_status not in ['completed', 'error', 'timeout']:
                    all_completed = False
                    print(f"Advertencia: {tool_name} no completó correctamente (estado: {tool_status})")

            # Actualizar resumen final
            self.results['summary']['vulnerabilities_found'] = len(self.results['vulnerabilities'])

            # Generar recomendaciones
            print("Generando recomendaciones...")
            self.generate_recommendations()

            # Determinar estado final
            if all_completed and self.results['summary']['failed_tools'] == 0:
                self.results['status'] = 'completed'
                self.results['progress'] = f'Análisis completado - {completed_count}/{total_tools} herramientas exitosas'
            elif completed_count > 0:
                self.results['status'] = 'completed_with_errors'
                self.results['progress'] = f'Completado con errores - {completed_count}/{total_tools} herramientas exitosas'
            else:
                self.results['status'] = 'failed'
                self.results['progress'] = 'Análisis falló - ninguna herramienta completó exitosamente'

            self.results['end_time'] = datetime.now().isoformat()
            print(f"Análisis terminado para: {self.target} con estado: {self.results['status']}")

        except Exception as e:
            self.results['status'] = 'error'
            self.results['error'] = str(e)
            self.results['progress'] = f'Error crítico: {str(e)[:100]}'
            self.results['end_time'] = datetime.now().isoformat()
            print(f"Error crítico en análisis: {str(e)}")

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/sw.js')
def service_worker():
    return send_file('static/sw.js', mimetype='application/javascript')

@app.route('/diagnostic')
def diagnostic():
    """Endpoint para diagnóstico del sistema"""
    try:
        tools_to_check = [
            ('whois', 'whois --version'),
            ('dig', 'dig -v'),
            ('theharvester', 'theharvester --version'),
            ('assetfinder', 'assetfinder --help'),
            ('nuclei', 'nuclei -version')
        ]

        diagnostic_results = {}

        for tool_name, check_cmd in tools_to_check:
            try:
                # Verificar si está en PATH
                which_result = subprocess.run(f"which {tool_name}", shell=True, capture_output=True, text=True, timeout=5)

                if which_result.returncode == 0:
                    path = which_result.stdout.strip()

                    # Verificar versión
                    try:
                        version_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                        if version_result.returncode == 0:
                            version_info = version_result.stdout.strip()[:200]
                        else:
                            version_info = version_result.stderr.strip()[:200]
                    except:
                        version_info = "No disponible"

                    diagnostic_results[tool_name] = {
                        'available': True,
                        'path': path,
                        'version': version_info,
                        'status': 'OK'
                    }
                else:
                    diagnostic_results[tool_name] = {
                        'available': False,
                        'path': None,
                        'version': None,
                        'status': 'NOT_FOUND'
                    }

            except Exception as e:
                diagnostic_results[tool_name] = {
                    'available': False,
                    'path': None,
                    'version': None,
                    'status': f'ERROR: {str(e)[:100]}'
                }

        # Resumen
        available_count = sum(1 for tool in diagnostic_results.values() if tool['available'])
        total_count = len(diagnostic_results)

        return jsonify({
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'available_tools': available_count,
                'total_tools': total_count,
                'percentage': round((available_count / total_count) * 100, 1)
            },
            'tools': diagnostic_results,
            'environment': {
                'port': os.environ.get('PORT', '3333'),
                'railway_env': os.environ.get('RAILWAY_ENVIRONMENT_NAME', 'local'),
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            }
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/scan', methods=['POST'])
def start_scan():
    # Limpiar scans antiguos antes de iniciar uno nuevo
    cleanup_old_scans()

    data = request.get_json()
    target = data.get('target', '').strip()

    if not target:
        return jsonify({'error': 'Target is required'}), 400

    # Validar formato del target - más permisivo
    if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9\-._]{0,253}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$', target):
        return jsonify({'error': f'Formato de dominio inválido: {target}. Usa formato como ejemplo.com'}), 400

    # Verificar límite de scans concurrentes
    if len(active_scans) >= MAX_CONCURRENT_SCANS:
        return jsonify({'error': f'Maximum concurrent scans ({MAX_CONCURRENT_SCANS}) reached'}), 429

    # Generar ID único para el análisis
    scan_id = str(uuid.uuid4())

    # Crear scanner
    scanner = SecurityScanner(target, scan_id)
    active_scans[scan_id] = scanner

    # Ejecutar análisis en hilo separado
    def run_scan_and_save():
        try:
            scanner.run_full_scan()
            # Guardar resultados en archivo para persistencia
            save_scan_results(scan_id, scanner.results)
            print(f"Scan {scan_id} completado y guardado")

            # Limpiar de memoria después de guardar
            import threading
            def cleanup_memory():
                import time
                time.sleep(300)  # 5 minutos después de completar
                if scan_id in active_scans:
                    del active_scans[scan_id]
                    print(f"Scan {scan_id} removido de memoria para liberar recursos")

            cleanup_thread = threading.Thread(target=cleanup_memory)
            cleanup_thread.daemon = True
            cleanup_thread.start()

        except Exception as e:
            print(f"Error en scan {scan_id}: {e}")
            # Guardar error también
            if scan_id in active_scans:
                scanner.results['status'] = 'error'
                scanner.results['error'] = str(e)
                save_scan_results(scan_id, scanner.results)

    thread = threading.Thread(target=run_scan_and_save)
    thread.daemon = True
    thread.start()

    return jsonify({
        'scan_id': scan_id,
        'message': 'Análisis iniciado correctamente'
    })

@app.route('/status/<scan_id>')
def get_scan_status(scan_id):
    try:
        # Primero buscar en scans activos
        if scan_id in active_scans:
            scanner = active_scans[scan_id]
            # Asegurar que el resultado tenga la estructura correcta
            if hasattr(scanner, 'results') and scanner.results:
                return jsonify(scanner.results)

        # Si no está activo, buscar en archivos guardados
        results = load_scan_results(scan_id)
        if results:
            return jsonify(results)

        # Si no se encuentra, devolver un estado por defecto
        return jsonify({
            'scan_id': scan_id,
            'status': 'not_found',
            'error': 'Scan not found or expired',
            'message': 'El scan no fue encontrado. Puede haber expirado o no existir.'
        }), 404

    except Exception as e:
        print(f"Error en get_scan_status: {e}")
        return jsonify({
            'scan_id': scan_id,
            'status': 'error',
            'error': str(e),
            'message': 'Error interno del servidor'
        }), 500

@app.route('/report/<scan_id>')
def generate_report(scan_id):
    # Buscar en scans activos primero
    results = None
    if scan_id in active_scans:
        scanner = active_scans[scan_id]
        results = scanner.results
    else:
        # Buscar en archivos guardados
        results = load_scan_results(scan_id)

    if not results:
        return jsonify({'error': 'Scan not found'}), 404

    # Permitir generar reporte si está completado o completado con errores
    if results['status'] not in ['completed', 'completed_with_errors']:
        return jsonify({'error': 'Scan not completed yet'}), 400

    # Generar reporte HTML
    report_html = render_template('report.html', results=results)

    # Guardar reporte
    target = results.get('target', 'unknown')
    report_filename = f"security_report_{scan_id}_{target}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    report_path = os.path.join(REPORTS_DIR, report_filename)

    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_html)

    return send_file(report_path, as_attachment=True, download_name=report_filename)

if __name__ == '__main__':
    # Crear directorio de reportes si no existe
    os.makedirs(REPORTS_DIR, exist_ok=True)

    # Limpiar scans antiguos al inicio
    cleanup_old_scans()

    # Configuración del puerto para Railway
    port = int(os.environ.get('PORT', 3333))

    print("=" * 60)
    print("🔒 Security Scanner Web Interface - DIAGNÓSTICO")
    print("=" * 60)

    # Ejecutar diagnóstico del sistema
    available_tools = diagnose_system()

    print("🔧 CARACTERÍSTICAS HABILITADAS:")
    print("✅ Service Worker funcionando")
    print("✅ Persistencia de scans habilitada")
    print("✅ Manejo robusto de errores")
    print("✅ Diagnóstico automático de herramientas")

    if 'theharvester' in available_tools:
        print("✅ theHarvester disponible")
    else:
        print("❌ theHarvester NO disponible")

    if 'nuclei' in available_tools:
        print("✅ Nuclei disponible")
    else:
        print("❌ Nuclei NO disponible")

    print("=" * 60)
    print(f"🌐 Servidor iniciando en puerto: {port}")
    if port == 3333:
        print("🏠 Modo local: http://localhost:3333")
    else:
        print("🚂 Modo Railway: Puerto automático")
    print("=" * 60)

    # Configuración optimizada para Railway
    app.run(
        debug=False,  # Desactivar debug en producción
        host='0.0.0.0',
        port=port,
        threaded=True  # Habilitar threading para mejor rendimiento
    )

