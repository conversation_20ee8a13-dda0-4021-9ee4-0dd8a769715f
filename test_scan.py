#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para verificar que el análisis funciona
"""

import requests
import time
import json

def test_scan():
    base_url = "https://securityscannerweb-production.up.railway.app"

    # Datos del escaneo
    scan_data = {
        "target": "example.com"
    }

    print("🔍 Iniciando prueba de escaneo...")

    try:
        # Iniciar escaneo
        response = requests.post(f"{base_url}/scan", json=scan_data)

        if response.status_code == 200:
            result = response.json()
            scan_id = result['scan_id']
            print(f"✅ Escaneo iniciado: {scan_id}")

            # Monitorear progreso
            for i in range(20):  # Máximo 20 intentos
                time.sleep(5)  # Esperar 5 segundos
                status_response = requests.get(f"{base_url}/status/{scan_id}")

                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print(f"📊 Estado: {status_data['status']}")

                    # Mostrar herramientas completadas
                    tools_results = status_data.get('tools_results', {})
                    for tool, result in tools_results.items():
                        print(f"  🔧 {tool}: {result.get('status', 'unknown')}")

                    if status_data['status'] in ['completed', 'error']:
                        print(f"🎉 Escaneo finalizado: {status_data['status']}")

                        # Mostrar resultados
                        vulnerabilities = status_data.get('vulnerabilities', [])
                        print(f"🚨 Vulnerabilidades encontradas: {len(vulnerabilities)}")

                        recommendations = status_data.get('recommendations', [])
                        print(f"💡 Recomendaciones: {len(recommendations)}")

                        break
                else:
                    print(f"❌ Error obteniendo estado: {status_response.status_code}")
            else:
                print("⏰ Timeout esperando resultados")

        else:
            print(f"❌ Error iniciando escaneo: {response.status_code}")
            print(f"   Respuesta: {response.text}")

    except requests.exceptions.ConnectionError:
        print("❌ No se puede conectar al servidor. ¿Está ejecutándose en el puerto 4000?")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_scan()

